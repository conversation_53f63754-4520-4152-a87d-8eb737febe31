package cn.com.sinosoft.os.beijingexitapply.model;

import java.util.Date;

/**
 * 北京出行申请 - 实体类.
 *
 * <AUTHOR>
 * @date: 2025/08/02 15:41:41
 * @version V1.0
 */
public class BeijingExitApply {
	// serialVersionUID.
	private static final long serialVersionUID = 1L;

	// 主键.
	private String id;

	// 申请人姓名.
	private String applyName;

	// 身份类型.
	private String identityType;

	// 科室.
	private String department;

	// 申请日期.
	private Date applyDate;

	// 开始时间.
	private Date startDate;

	// 结束时间.
	private Date endDate;

	// 出行天数.
	private Date travelDays;

	// 目的地.
	private String applyDestn;

	// 出行事由.
	private String travelReason;

	// 流程标识.
	private String piId;

	// 审核状态.
	private String auditState;

	// 所领导.
	private String leader;

	// 添加地区.
	private String addZone;

	// 添加机构.
	private String addOrg;

	// 添加部门.
	private String addDep;

	// 添加用户.
	private String addUser;

	// 添加时间.
	private Date addTime;

	// 修改地区.
	private String modyZone;

	// 修改机构.
	private String modyOrg;

	// 修改部门.
	private String modyDep;

	// 修改用户.
	private String modyUser;

	// 修改时间.
	private Date modyTime;

	// 数据有效性.
	private String state;

	// 数据来源-数据来源.
	private String dataSource;

	// 数据来源-修改时间.
	private Date dataModyTime;

	/**
	 * 获取 主键.
	 *
	 * @return 主键
	 */
	public String getId() {
		return id;
	}

	/**
	 * 设置 主键.
	 *
	 * @param id 主键
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * 获取 申请人姓名.
	 *
	 * @return 申请人姓名
	 */
	public String getApplyName() {
		return applyName;
	}

	/**
	 * 设置 申请人姓名.
	 *
	 * @param applyName 申请人姓名
	 */
	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}

	/**
	 * 获取 身份类型.
	 *
	 * @return 身份类型
	 */
	public String getIdentityType() {
		return identityType;
	}

	/**
	 * 设置 身份类型.
	 *
	 * @param identityType 身份类型
	 */
	public void setIdentityType(String identityType) {
		this.identityType = identityType;
	}

	/**
	 * 获取 科室.
	 *
	 * @return 科室
	 */
	public String getDepartment() {
		return department;
	}

	/**
	 * 设置 科室.
	 *
	 * @param department 科室
	 */
	public void setDepartment(String department) {
		this.department = department;
	}

	/**
	 * 获取 申请日期.
	 *
	 * @return 申请日期
	 */
	public Date getApplyDate() {
		return applyDate;
	}

	/**
	 * 设置 申请日期.
	 *
	 * @param applyDate 申请日期
	 */
	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	/**
	 * 获取 开始时间.
	 *
	 * @return 开始时间
	 */
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * 设置 开始时间.
	 *
	 * @param startDate 开始时间
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * 获取 结束时间.
	 *
	 * @return 结束时间
	 */
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * 设置 结束时间.
	 *
	 * @param endDate 结束时间
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/**
	 * 获取 出行天数.
	 *
	 * @return 出行天数
	 */
	public Date getTravelDays() {
		return travelDays;
	}

	/**
	 * 设置 出行天数.
	 *
	 * @param travelDays 出行天数
	 */
	public void setTravelDays(Date travelDays) {
		this.travelDays = travelDays;
	}

	/**
	 * 获取 目的地.
	 *
	 * @return 目的地
	 */
	public String getApplyDestn() {
		return applyDestn;
	}

	/**
	 * 设置 目的地.
	 *
	 * @param applyDestn 目的地
	 */
	public void setApplyDestn(String applyDestn) {
		this.applyDestn = applyDestn;
	}

	/**
	 * 获取 出行事由.
	 *
	 * @return 出行事由
	 */
	public String getTravelReason() {
		return travelReason;
	}

	/**
	 * 设置 出行事由.
	 *
	 * @param travelReason 出行事由
	 */
	public void setTravelReason(String travelReason) {
		this.travelReason = travelReason;
	}

	/**
	 * 获取 流程标识.
	 *
	 * @return 流程标识
	 */
	public String getPiId() {
		return piId;
	}

	/**
	 * 设置 流程标识.
	 *
	 * @param piId 流程标识
	 */
	public void setPiId(String piId) {
		this.piId = piId;
	}

	/**
	 * 获取 审核状态.
	 *
	 * @return 审核状态
	 */
	public String getAuditState() {
		return auditState;
	}

	/**
	 * 设置 审核状态.
	 *
	 * @param auditState 审核状态
	 */
	public void setAuditState(String auditState) {
		this.auditState = auditState;
	}

	/**
	 * 获取 所领导.
	 *
	 * @return 所领导
	 */
	public String getLeader() {
		return leader;
	}

	/**
	 * 设置 所领导.
	 *
	 * @param leader 所领导
	 */
	public void setLeader(String leader) {
		this.leader = leader;
	}

	/**
	 * 获取 添加地区.
	 *
	 * @return 添加地区
	 */
	public String getAddZone() {
		return addZone;
	}

	/**
	 * 设置 添加地区.
	 *
	 * @param addZone 添加地区
	 */
	public void setAddZone(String addZone) {
		this.addZone = addZone;
	}

	/**
	 * 获取 添加机构.
	 *
	 * @return 添加机构
	 */
	public String getAddOrg() {
		return addOrg;
	}

	/**
	 * 设置 添加机构.
	 *
	 * @param addOrg 添加机构
	 */
	public void setAddOrg(String addOrg) {
		this.addOrg = addOrg;
	}

	/**
	 * 获取 添加部门.
	 *
	 * @return 添加部门
	 */
	public String getAddDep() {
		return addDep;
	}

	/**
	 * 设置 添加部门.
	 *
	 * @param addDep 添加部门
	 */
	public void setAddDep(String addDep) {
		this.addDep = addDep;
	}

	/**
	 * 获取 添加用户.
	 *
	 * @return 添加用户
	 */
	public String getAddUser() {
		return addUser;
	}

	/**
	 * 设置 添加用户.
	 *
	 * @param addUser 添加用户
	 */
	public void setAddUser(String addUser) {
		this.addUser = addUser;
	}

	/**
	 * 获取 添加时间.
	 *
	 * @return 添加时间
	 */
	public Date getAddTime() {
		return addTime;
	}

	/**
	 * 设置 添加时间.
	 *
	 * @param addTime 添加时间
	 */
	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	/**
	 * 获取 修改地区.
	 *
	 * @return 修改地区
	 */
	public String getModyZone() {
		return modyZone;
	}

	/**
	 * 设置 修改地区.
	 *
	 * @param modyZone 修改地区
	 */
	public void setModyZone(String modyZone) {
		this.modyZone = modyZone;
	}

	/**
	 * 获取 修改机构.
	 *
	 * @return 修改机构
	 */
	public String getModyOrg() {
		return modyOrg;
	}

	/**
	 * 设置 修改机构.
	 *
	 * @param modyOrg 修改机构
	 */
	public void setModyOrg(String modyOrg) {
		this.modyOrg = modyOrg;
	}

	/**
	 * 获取 修改部门.
	 *
	 * @return 修改部门
	 */
	public String getModyDep() {
		return modyDep;
	}

	/**
	 * 设置 修改部门.
	 *
	 * @param modyDep 修改部门
	 */
	public void setModyDep(String modyDep) {
		this.modyDep = modyDep;
	}

	/**
	 * 获取 修改用户.
	 *
	 * @return 修改用户
	 */
	public String getModyUser() {
		return modyUser;
	}

	/**
	 * 设置 修改用户.
	 *
	 * @param modyUser 修改用户
	 */
	public void setModyUser(String modyUser) {
		this.modyUser = modyUser;
	}

	/**
	 * 获取 修改时间.
	 *
	 * @return 修改时间
	 */
	public Date getModyTime() {
		return modyTime;
	}

	/**
	 * 设置 修改时间.
	 *
	 * @param modyTime 修改时间
	 */
	public void setModyTime(Date modyTime) {
		this.modyTime = modyTime;
	}

	/**
	 * 获取 数据有效性.
	 *
	 * @return 数据有效性
	 */
	public String getState() {
		return state;
	}

	/**
	 * 设置 数据有效性.
	 *
	 * @param state 数据有效性
	 */
	public void setState(String state) {
		this.state = state;
	}

	/**
	 * 获取 数据来源.
	 *
	 * @return 数据来源
	 */
	public String getDataSource() {
		return dataSource;
	}

	/**
	 * 设置 数据来源.
	 *
	 * @param dataSource 数据来源
	 */
	public void setDataSource(String dataSource) {
		this.dataSource = dataSource;
	}

	/**
	 * 获取 数据修改时间.
	 *
	 * @return 数据修改时间
	 */
	public Date getDataModyTime() {
		return dataModyTime;
	}

	/**
	 * 设置 数据修改时间.
	 *
	 * @param dataModyTime 数据修改时间
	 */
	public void setDataModyTime(Date dataModyTime) {
		this.dataModyTime = dataModyTime;
	}


}



